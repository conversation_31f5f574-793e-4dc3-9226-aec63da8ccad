import 'package:account_management/account_management.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:analytics/analytics.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_svg/svg.dart';
import '../../custom_widgets/emoji_slider.dart';

class TherapyFeedbackBottomSheet extends StatefulWidget {
  final String sessionId;

  const TherapyFeedbackBottomSheet({
    Key? key,
    required this.sessionId,
  }) : super(key: key);

  @override
  State<TherapyFeedbackBottomSheet> createState() =>
      _TherapyFeedbackBottomSheetState();

  static void show(BuildContext context, String sessionId) {
    try {
      showModalBottomSheet<void>(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => BlocProvider(
          create: (context) => GetIt.instance<TherapyFeedbackBloc>()
            ..add(TherapyFeedbackEvent.loadSessionData(sessionId)),
          child: TherapyFeedbackBottomSheet(sessionId: sessionId),
        ),
      );
    } catch (e) {
      print('❌ Error showing therapy feedback bottom sheet: $e');
      // Fallback: try to show a simple dialog instead
      _showFallbackDialog(context, sessionId);
    }
  }

  static void _showFallbackDialog(BuildContext context, String sessionId) {
    try {
      showDialog<void>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Therapy Feedback'),
          content: const Text(
              'How was your therapy session? We would love to hear your feedback!'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Skip'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Try to show the bottom sheet again after a delay
                Future.delayed(const Duration(milliseconds: 500), () {
                  show(context, sessionId);
                });
              },
              child: const Text('Give Feedback'),
            ),
          ],
        ),
      );
    } catch (e) {
      print('❌ Error showing fallback dialog: $e');
    }
  }
}

class _TherapyFeedbackBottomSheetState
    extends State<TherapyFeedbackBottomSheet> {
  final TextEditingController _feedbackController = TextEditingController();
  int _painLevelBefore = 5;
  int _painLevelAfter = 5;
  List<SymptomModel> _selectedSymptoms = [];

  @override
  void dispose() {
    _feedbackController.dispose();
    super.dispose();
  }

  List<String> emoji = [
    '\u{1F601}', // 😁
    '\u{1F642}', // 🙂
    '\u{1F610}', // 😐
    '\u{1F615}', // 😕
    '\u{1F641}', // 🙁
    '\u{1F61E}', // 😞
    '\u{1F613}', // 😓
    '\u{1F623}', // 😣
    '\u{1F616}', // 😖
    '\u{1F635}\u{200D}\u{1F4AB}', // 😵‍💫
    '\u{1F635}\u{200D}\u{1F4AB}', // 😵‍💫
  ];

  List<String> pain_description = [
    'No Pain',
    'Discomfort',
    'Very Mild',
    'Mild',
    'Moderate',
    'Significant',
    'High',
    'Very High',
    'Intense',
    'Worst Pain',
    'Worst Pain',
  ];
  List<SymptomModel> _symptoms = [
    SymptomModel(name: 'Headache'),
    SymptomModel(name: 'Fatigue'),
    SymptomModel(name: 'Bloating'),
    SymptomModel(name: 'Back Pain'),
    SymptomModel(name: 'Cramps'),
    SymptomModel(name: 'Breakouts'),
  ];
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: DraggableScrollableSheet(
        initialChildSize: 0.8,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        expand: false,
        builder: (context, scrollController) {
          return BlocConsumer<TherapyFeedbackBloc, TherapyFeedbackState>(
            listener: (context, state) {
              state.when(
                initial: () {},
                loading: () {},
                sessionLoaded: (session) {},
                submitting: () {},
                submitted: () {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Thank you for your feedback!'),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
                error: (message) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(message),
                      backgroundColor: Colors.red,
                    ),
                  );
                },
              );
            },
            builder: (context, state) {
              return SingleChildScrollView(
                controller: scrollController,
                padding: EdgeInsets.all(20.r),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Handle bar
                    Center(
                      child: Container(
                        width: 40.w,
                        height: 4.h,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(2.r),
                        ),
                      ),
                    ),
                    SizedBox(height: 20.h),

                    // Title
                    Text(
                      'How was your therapy session?',
                      style: TextStyle(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 20.h),

                    // Session details section
                    state.when(
                      initial: () => const SizedBox(),
                      loading: () =>
                          const Center(child: CircularProgressIndicator()),
                      sessionLoaded: (session) => _buildSessionDetails(session),
                      submitting: () =>
                          const Center(child: CircularProgressIndicator()),
                      submitted: () => const SizedBox(),
                      error: (message) => const SizedBox(),
                    ),

                    SizedBox(height: 20.h),

                    // Pain level before therapy
                    _buildPainLevelSection(
                      title: 'Pain Level Before Therapy',
                      value: _painLevelBefore,
                      onChanged: (value) =>
                          setState(() => _painLevelBefore = value),
                    ),
                    SizedBox(height: 20.h),

                    // Pain level after therapy
                    _buildPainLevelSection(
                      title: 'Pain Level After Therapy',
                      value: _painLevelAfter,
                      onChanged: (value) =>
                          setState(() => _painLevelAfter = value),
                    ),
                    SizedBox(height: 20.h),

                    // Symptoms section
                    _buildSymptomsSection(),
                    SizedBox(height: 20.h),

                    // Feedback text
                    _buildSectionTitle('Additional feedback (optional)'),
                    SizedBox(height: 10.h),
                    TextField(
                      controller: _feedbackController,
                      maxLines: 4,
                      decoration: InputDecoration(
                        hintText:
                            'Share your thoughts about this therapy session...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        contentPadding: EdgeInsets.all(16.r),
                      ),
                    ),
                    SizedBox(height: 30.h),

                    // Submit button
                    SizedBox(
                      width: double.infinity,
                      height: 50.h,
                      child: ElevatedButton(
                        onPressed: state.maybeWhen(
                          submitting: () => null,
                          orElse: () => _submitFeedback,
                        ),
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                        ),
                        child: state.when(
                          submitting: () => const CircularProgressIndicator(
                              color: Colors.white),
                          initial: () => Text('Submit Feedback',
                              style: TextStyle(fontSize: 16.sp)),
                          loading: () => Text('Submit Feedback',
                              style: TextStyle(fontSize: 16.sp)),
                          sessionLoaded: (session) => Text('Submit Feedback',
                              style: TextStyle(fontSize: 16.sp)),
                          submitted: () => Text('Submit Feedback',
                              style: TextStyle(fontSize: 16.sp)),
                          error: (message) => Text('Submit Feedback',
                              style: TextStyle(fontSize: 16.sp)),
                        ),
                      ),
                    ),
                    SizedBox(height: 10.h),

                    // Skip button
                    SizedBox(
                      width: double.infinity,
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: Text(
                          'Skip for now',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                    ),

                    // Add bottom padding for keyboard
                    SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildSessionDetails(TherapySessionModel session) {
    final startTime = session.sessionInfo.therapyStartTime;
    final endTime = session.sessionInfo.therapyEndTime;
    final duration = endTime != null && startTime != null
        ? endTime.difference(startTime)
        : null;

    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Session Details',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: Colors.blue[800],
            ),
          ),
          SizedBox(height: 12.h),
          _buildDetailRow('Date',
              DateFormat('MMM dd, yyyy').format(startTime ?? DateTime.now())),
          _buildDetailRow('Start Time',
              DateFormat('h:mm a').format(startTime ?? DateTime.now())),
          if (endTime != null)
            _buildDetailRow('End Time', DateFormat('h:mm a').format(endTime)),
          if (duration != null)
            _buildDetailRow('Duration', '${duration.inMinutes} minutes'),
          _buildDetailRow(
              'Heat Level', '${session.mostUsedSettings.heatLevel}'),
          _buildDetailRow(
              'TENS Level', '${session.mostUsedSettings.tensLevel}'),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xff26204a),
          ),
    );
  }

  Widget _buildPainLevelSection({
    required String title,
    required int value,
    required void Function(int) onChanged,
  }) {
    return Container(
      margin: EdgeInsets.zero,
      height: 0.6.sw,
      width: 0.90.sw,
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Color(0x40000000),
            blurRadius: 4.0,
            offset: Offset(0, 1),
          ),
        ],
        borderRadius: BorderRadius.circular(32),
        color: Color.fromRGBO(250, 242, 223, 1),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 30.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 10),
            Text(
              title,
              style: TextStyle(
                color: Color.fromRGBO(58, 38, 101, 1.0),
                fontWeight: FontWeight.w700,
                fontSize: 22,
              ),
            ),
            SizedBox(height: 30),
            Text(
              value.toString(),
              style: TextStyle(
                color: Color.fromRGBO(58, 38, 101, 1.0),
                fontWeight: FontWeight.w700,
                fontSize: 31,
              ),
            ),
            SizedBox(height: 10),
            Container(
              height: 77,
              margin: EdgeInsets.zero,
              child: Align(
                child: EmojiSlider(
                  key: Key(
                      'pain_slider_${title.toLowerCase().replaceAll(' ', '_')}'),
                  currentValue: value.toDouble(),
                  emojis: emoji,
                  minValue: 0,
                  maxValue: 10,
                  labels: pain_description,
                  onChanged: (handlerIndex, lowerValue, upperValue) {
                    onChanged((lowerValue as double).toInt());
                  },
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 5.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'No Pain',
                    style: TextStyle(color: Colors.black),
                  ),
                  Text(
                    'Worst Pain',
                    style: TextStyle(color: Colors.black),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildSymptomsSection() {
    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Color(0x40000000),
            blurRadius: 4.0,
            offset: Offset(0, 1),
          ),
        ],
        borderRadius: BorderRadius.circular(32),
        color: Color.fromRGBO(250, 242, 223, 1),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: 10),
          Text(
            "Symptoms",
            style: TextStyle(
              color: Color.fromRGBO(58, 38, 101, 1.0),
              fontWeight: FontWeight.w700,
              fontSize: 22,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(15.0),
            child: GridView.builder(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 10.0,
                mainAxisSpacing: 0.0,
                mainAxisExtent: 80.0,
              ),
              itemCount: _symptoms.length,
              itemBuilder: (gridContext, index) {
                final isSelected = _selectedSymptoms
                    .any((symptom) => symptom.name == _symptoms[index].name);
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      if (isSelected) {
                        _selectedSymptoms.removeWhere(
                            (symptom) => symptom.name == _symptoms[index].name);
                      } else {
                        _selectedSymptoms.add(_symptoms[index]);
                      }
                    });
                  },
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        height: 38,
                        width: 67,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(32),
                          color: isSelected
                              ? Color.fromRGBO(247, 166, 0, 1)
                              : Colors.white,
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(4.0),
                          child: SvgPicture.asset(
                            'assets/home/<USER>' ', '_')}.svg',
                            colorFilter: ColorFilter.mode(
                              isSelected
                                  ? Colors.white
                                  : Color.fromRGBO(88, 66, 148, 1),
                              BlendMode.srcIn,
                            ),
                            fit: BoxFit.fitHeight,
                            height: 20,
                            width: 20,
                          ),
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        _symptoms[index].name,
                        style: TextStyle(
                          color: Color.fromRGBO(58, 38, 101, 1.0),
                          fontWeight: FontWeight.w700,
                          fontSize: 15,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _submitFeedback() {
    // First, store the symptoms and pain level in the period tracking system (today's date)
    // This will store the symptoms and pain level before therapy as "overall pain level" in the symptom tracking system
    try {
      // Select today's date first
      context.read<ManagePeriodTrackingBloc>().add(
            ManagePeriodTrackingEvent.selectDay(DateTime.now(), false),
          );

      // Set the pain level before therapy as the overall pain level
      context.read<ManagePeriodTrackingBloc>().add(
            ManagePeriodTrackingEvent.changePainLevel(_painLevelBefore),
          );

      // Add all selected symptoms
      for (final symptom in _selectedSymptoms) {
        context.read<ManagePeriodTrackingBloc>().add(
              ManagePeriodTrackingEvent.toggleSymptom(symptom),
            );
      }

      print('✅ Stored symptoms and pain level in period tracking system');
    } catch (e) {
      print('❌ Error storing symptoms and pain level: $e');
      // Continue with feedback submission even if symptom storage fails
    }

    // Then submit the therapy feedback (pain levels before/after and text feedback)
    context.read<TherapyFeedbackBloc>().add(
          TherapyFeedbackEvent.submitFeedback(
            sessionId: widget.sessionId,
            feedbackText: _feedbackController.text.trim().isEmpty
                ? null
                : _feedbackController.text.trim(),
            painLevelBefore: _painLevelBefore,
            painLevelAfter: _painLevelAfter,
          ),
        );
  }
}
