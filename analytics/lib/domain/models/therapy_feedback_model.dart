import 'package:analytics/domain/models/timestamp_converters.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';

part 'therapy_feedback_model.g.dart';

@JsonSerializable(explicitToJson: true)
class TherapyFeedbackModel {
  final String? feedbackText;
  final int? painLevelBefore;
  final int? painLevelAfter;
  final bool? feedbackRequested;
  final bool? feedbackCompleted;

  @JsonKey(
      fromJson: firestoreTimestampFromJson, toJson: firestoreTimestampToJson)
  final DateTime? feedbackSubmittedAt;

  TherapyFeedbackModel({
    this.feedbackText,
    this.painLevelBefore,
    this.painLevelAfter,
    this.satisfactionLevel,
    this.symptoms,
    this.feedbackRequested = false,
    this.feedbackCompleted = false,
    this.feedbackSubmittedAt,
  });

  // === 🔁 Firestore serialization ===
  factory TherapyFeedbackModel.fromJson(Map<String, dynamic> json) =>
      _$TherapyFeedbackModelFromJson(json);

  Map<String, dynamic> toJson() => _$TherapyFeedbackModelToJson(this);

  // === 🔁 Local JSON serialization ===
  factory TherapyFeedbackModel.fromLocalJson(Map<String, dynamic> json) {
    return TherapyFeedbackModel(
      feedbackText: json['feedbackText'] as String?,
      painLevelBefore: json['painLevelBefore'] as int?,
      painLevelAfter: json['painLevelAfter'] as int?,
      feedbackRequested: json['feedbackRequested'] as bool? ?? false,
      feedbackCompleted: json['feedbackCompleted'] as bool? ?? false,
      feedbackSubmittedAt: json['feedbackSubmittedAt'] != null
          ? DateTime.tryParse(json['feedbackSubmittedAt'] as String)
          : null,
    );
  }

  Map<String, dynamic> toLocalJson() {
    return {
      'feedbackText': feedbackText,
      'painLevelBefore': painLevelBefore,
      'painLevelAfter': painLevelAfter,
      'feedbackRequested': feedbackRequested,
      'feedbackCompleted': feedbackCompleted,
      'feedbackSubmittedAt': feedbackSubmittedAt != null
          ? localTimestampToJson(feedbackSubmittedAt)
          : null,
    };
  }

  // === 🧬 copyWith ===
  TherapyFeedbackModel copyWith({
    String? feedbackText,
    int? painLevelBefore,
    int? painLevelAfter,
    bool? feedbackRequested,
    bool? feedbackCompleted,
    DateTime? feedbackSubmittedAt,
  }) {
    return TherapyFeedbackModel(
      feedbackText: feedbackText ?? this.feedbackText,
      painLevelBefore: painLevelBefore ?? this.painLevelBefore,
      painLevelAfter: painLevelAfter ?? this.painLevelAfter,
      feedbackRequested: feedbackRequested ?? this.feedbackRequested,
      feedbackCompleted: feedbackCompleted ?? this.feedbackCompleted,
      feedbackSubmittedAt: feedbackSubmittedAt ?? this.feedbackSubmittedAt,
    );
  }

  // === 🏁 Create Empty Feedback ===
  factory TherapyFeedbackModel.empty() {
    return TherapyFeedbackModel(
      feedbackText: null,
      painLevelBefore: null,
      painLevelAfter: null,
      feedbackRequested: false,
      feedbackCompleted: false,
      feedbackSubmittedAt: null,
    );
  }
}
