// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'therapy_feedback_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SymptomModel _$SymptomModelFromJson(Map<String, dynamic> json) => SymptomModel(
      name: json['name'] as String,
    );

Map<String, dynamic> _$SymptomModelToJson(SymptomModel instance) =>
    <String, dynamic>{
      'name': instance.name,
    };

TherapyFeedbackModel _$TherapyFeedbackModelFromJson(
        Map<String, dynamic> json) =>
    TherapyFeedbackModel(
      feedbackText: json['feedbackText'] as String?,
      painLevelBefore: (json['painLevelBefore'] as num?)?.toInt(),
      painLevelAfter: (json['painLevelAfter'] as num?)?.toInt(),
      satisfactionLevel: (json['satisfactionLevel'] as num?)?.toInt(),
      symptoms: (json['symptoms'] as List<dynamic>?)
          ?.map((e) => SymptomModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      feedbackRequested: json['feedbackRequested'] as bool? ?? false,
      feedbackCompleted: json['feedbackCompleted'] as bool? ?? false,
      feedbackSubmittedAt:
          firestoreTimestampFromJson(json['feedbackSubmittedAt']),
    );

Map<String, dynamic> _$TherapyFeedbackModelToJson(
        TherapyFeedbackModel instance) =>
    <String, dynamic>{
      'feedbackText': instance.feedbackText,
      'painLevelBefore': instance.painLevelBefore,
      'painLevelAfter': instance.painLevelAfter,
      'satisfactionLevel': instance.satisfactionLevel,
      'symptoms': instance.symptoms?.map((e) => e.toJson()).toList(),
      'feedbackRequested': instance.feedbackRequested,
      'feedbackCompleted': instance.feedbackCompleted,
      'feedbackSubmittedAt':
          firestoreTimestampToJson(instance.feedbackSubmittedAt),
    };
