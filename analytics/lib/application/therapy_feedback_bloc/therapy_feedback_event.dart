part of 'therapy_feedback_bloc.dart';

@freezed
class TherapyFeedbackEvent with _$TherapyFeedbackEvent {
  const factory TherapyFeedbackEvent.loadSessionData(String sessionId) =
      _LoadSessionData;

  const factory TherapyFeedbackEvent.submitFeedback({
    required String sessionId,
    String? feedbackText,
    int? painLevelBefore,
    int? painLevelAfter,
    int? satisfactionLevel,
  }) = _SubmitFeedback;

  const factory TherapyFeedbackEvent.resetState() = _ResetState;
}
