# Therapy Session Feedback Feature

## Overview
This feature provides a comprehensive therapy session feedback system that:
- Automatically schedules feedback notifications after therapy sessions end
- Shows a beautiful bottom sheet for collecting user feedback
- Stores feedback data both locally and in the cloud
- <PERSON><PERSON> notification taps when the app is in background or foreground
- Works seamlessly with the existing analytics and session management system

## Components

### 1. Enhanced TherapySessionModel
**Location**: `analytics/lib/domain/models/therapy_session_model.dart`

New feedback-related fields:
- `feedbackText: String?` - Optional user feedback text
- `painLevelBefore: int?` - Pain level before therapy (0-10)
- `painLevelAfter: int?` - Pain level after therapy (0-10)
- `satisfactionLevel: int?` - Satisfaction rating (1-5)
- `feedbackRequested: bool?` - Whether feedback was requested
- `feedbackCompleted: bool?` - Whether feedback was completed
- `feedbackSubmittedAt: DateTime?` - When feedback was submitted

### 2. Analytics Facade Extensions
**Location**: `analytics/lib/domain/facade/analytics_facade.dart`

New methods:
```dart
Future<Either<String, Unit>> submitSessionFeedback({
  required String sessionId,
  String? feedbackText,
  int? painLevelBefore,
  int? painLevelAfter,
  int? satisfactionLevel,
});

Future<Either<String, Unit>> scheduleSessionFeedbackNotification({
  required String sessionId,
  required Duration delay,
});

Future<Either<String, Unit>> markFeedbackRequested(String sessionId);
```

### 3. Session State Manager Integration
**Location**: `analytics/lib/infrastructure/services/session_state_manager.dart`

- Automatically schedules feedback notification 5 minutes after session ends
- Marks session as feedback requested
- Uses the notifications module to schedule the notification

### 4. Therapy Feedback Bloc
**Location**: `app/lib/application/therapy_feedback_bloc/`

State management for the feedback UI:
- `LoadSessionData` - Load session details for display
- `SubmitFeedback` - Submit user feedback
- States: Initial, Loading, SessionLoaded, Submitting, Submitted, Error

### 5. Therapy Feedback Bottom Sheet
**Location**: `app/lib/widgets/therapy_feedback_bottom_sheet.dart`

Beautiful, comprehensive feedback UI:
- Session details display (date, time, duration, settings)
- Pain level sliders (before/after therapy)
- Satisfaction rating slider
- Optional feedback text area
- Submit/Skip buttons
- Proper keyboard handling and responsive design

### 6. Therapy Feedback Service
**Location**: `app/lib/services/therapy_feedback_service.dart`

Handles the feedback workflow:
- Schedules notifications
- Handles notification taps
- Shows feedback UI at appropriate times
- Manages pending feedback requests

## Integration Guide

### 1. Automatic Session End Integration
When a therapy session ends, the `SessionStateManager` automatically:
1. Schedules a feedback notification for 5 minutes later
2. Marks the session as feedback requested
3. The notification includes the session ID as payload

### 2. Notification Handling
When a user taps the feedback notification:
1. The notification service receives the payload (session ID)
2. If app is in foreground, immediately shows feedback bottom sheet
3. If app is in background, stores session ID for later
4. When app becomes active, checks for pending feedback and shows UI

### 3. In-App Feedback (Foreground)
When the app is in foreground during session end:
1. Session ends and notification is scheduled
2. After a 2-second delay, shows in-app feedback request
3. User can provide feedback immediately without waiting for notification

### 4. Feedback Data Flow
1. User submits feedback via bottom sheet
2. Feedback data is validated and submitted via AnalyticsFacade
3. Session is updated with feedback data
4. Data is saved locally (SharedPreferences)
5. Data is synced to cloud (Firestore)
6. User receives success confirmation

## Usage Examples

### Testing the Feature
Use the demo page: `app/lib/pages/therapy_feedback_demo_page.dart`

```dart
// Navigate to demo page
Navigator.push(context, MaterialPageRoute(
  builder: (context) => TherapyFeedbackDemoPage(),
));
```

### Manual Feedback Request
```dart
// Show feedback for a specific session
TherapyFeedbackBottomSheet.show(context, sessionId);
```

### Schedule Feedback Notification
```dart
final feedbackService = TherapyFeedbackService(
  GetIt.instance<ScheduledNotificationsFacade>(),
  GetIt.instance<IAnalyticsFacade>(),
  navigatorKey,
);

await feedbackService.scheduleTherapyFeedback(
  sessionId: sessionId,
  afterDuration: Duration(minutes: 5),
);
```

### Check for Pending Feedback
```dart
// Call this when app becomes active
await feedbackService.handlePendingFeedback();
```

## Data Structure

### Feedback Data in Session
```dart
{
  "feedbackText": "The therapy was very helpful for my back pain",
  "painLevelBefore": 8,
  "painLevelAfter": 3,
  "satisfactionLevel": 5,
  "feedbackRequested": true,
  "feedbackCompleted": true,
  "feedbackSubmittedAt": "2025-01-16T10:30:00.000Z"
}
```

### Notification Payload
```dart
{
  "sessionId": "uuid-of-session",
  "notificationType": "therapy_feedback",
  "title": "Therapy Feedback",
  "body": "How was your therapy session? Please share your feedback."
}
```

## Architecture Benefits

### 1. Non-Intrusive
- Only shows feedback when appropriate
- Doesn't block therapy workflow
- Handles both foreground and background scenarios

### 2. Robust Data Handling
- Graceful error handling
- Local storage with cloud sync
- Proper state management

### 3. Flexible Timing
- Configurable notification delay
- Immediate in-app requests when appropriate
- Handles missed notifications

### 4. User Experience
- Beautiful, intuitive UI
- Optional feedback (user can skip)
- Visual session details for context
- Multiple feedback dimensions (pain, satisfaction, text)

## Future Enhancements

1. **Analytics Dashboard**: Aggregate feedback data for insights
2. **Smart Scheduling**: Adjust notification timing based on user preferences
3. **Feedback Trends**: Show user their feedback history and trends
4. **Personalized Questions**: Customize feedback questions based on therapy type
5. **Reminder System**: Gentle reminders for missed feedback requests

## Troubleshooting

### Common Issues
1. **Notifications not showing**: Check notification permissions
2. **Bottom sheet not opening**: Ensure proper context and navigation key
3. **Data not saving**: Check analytics facade initialization
4. **DI errors**: Ensure all packages are properly configured

### Debug Mode
Enable detailed logging by setting debug flags in:
- `SessionStateManager`
- `TherapyFeedbackService`
- `AnalyticsFacadeImpl`

## Testing Checklist

- [ ] Session ends and notification is scheduled
- [ ] Notification appears at correct time
- [ ] Tapping notification opens feedback UI
- [ ] Feedback UI shows correct session details
- [ ] All feedback inputs work properly
- [ ] Feedback submission saves data
- [ ] Data syncs to cloud
- [ ] Error states are handled gracefully
- [ ] Skip functionality works
- [ ] Pending feedback handling works
- [ ] In-app feedback requests work
