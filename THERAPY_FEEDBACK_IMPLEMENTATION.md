# Therapy Feedback Feature Implementation

## Overview
This document describes the implementation of the therapy feedback feature that requests user feedback at the end of therapy sessions. The feature handles both foreground and background scenarios, prioritizes the latest session, and ensures feedback is collected for most therapy sessions.

## Architecture

### Core Components

1. **SessionStateManager** (`analytics/lib/infrastructure/services/session_state_manager.dart`)
   - Schedules feedback notification at session start (75 seconds later)
   - Handles foreground feedback detection when session ends
   - Sets immediate feedback flags for foreground scenarios

2. **TherapyFeedbackService** (`app/lib/services/therapy_feedback_service.dart`)
   - Manages feedback notification handling
   - Checks for pending feedback on app resume
   - Prioritizes latest session for feedback requests
   - Handles immediate feedback flags

3. **TherapyFeedbackBottomSheet** (`app/lib/widgets/therapy_feedback_bottom_sheet.dart`)
   - Displays session details and feedback form
   - Collects pain levels, satisfaction, and text feedback
   - Submits feedback via TherapyFeedbackBloc

4. **App Lifecycle Integration** (`app/lib/app.dart`)
   - Monitors app lifecycle changes
   - Triggers pending feedback checks on app resume

## Workflow

### 1. Session Start
```
Session starts → Notification scheduled for 75 seconds later → Session marked as feedbackRequested
```

### 2. Session End - Foreground
```
Session ends (app active) → Immediate feedback flag set → 2-second delay → Show feedback bottom sheet
```

### 3. Session End - Background
```
Session ends (app inactive) → Notification fires → User taps notification → Show feedback bottom sheet
```

### 4. App Resume
```
App becomes active → Check immediate feedback flag → Check latest session for pending feedback → Show bottom sheet if needed
```

## Key Features

### ✅ Smart Notification Timing
- Notification scheduled at session **start** (not end) to handle app closure scenarios
- 75-second delay ensures notification fires after typical session duration

### ✅ Foreground Detection
- Detects when session ends while app is in foreground
- Shows immediate feedback request instead of waiting for notification
- Uses SharedPreferences flags for cross-component communication

### ✅ Latest Session Priority
- Only requests feedback for the most recent therapy session
- Prevents overwhelming users with multiple feedback requests
- Checks session completion status and feedback status

### ✅ Robust Pending Feedback Handling
- Checks for pending feedback on every app resume
- Handles both notification-triggered and immediate feedback scenarios
- Clears old feedback flags (>5 minutes) to prevent stale requests

### ✅ Complete Data Flow
- Feedback stored in TherapySessionModel with all required fields
- Local storage via SharedPreferences
- Cloud sync via Firestore with proper serialization
- Feedback includes: painLevelBefore, painLevelAfter, satisfactionLevel, feedbackText

## Data Structure

### TherapySessionModel Feedback Fields
```dart
final String? feedbackText;
final int? painLevelBefore;
final int? painLevelAfter;
final int? satisfactionLevel;
final bool? feedbackRequested;
final bool? feedbackCompleted;
final DateTime? feedbackSubmittedAt;
```

### SharedPreferences Keys
- `immediate_feedback_session`: Session ID for immediate feedback
- `immediate_feedback_timestamp`: Timestamp when flag was set
- `pending_feedback_session`: Session ID from notification tap
- `last_therapy_session_id`: Fallback session ID

## Integration Points

### Analytics Package
- Session management and feedback scheduling
- Data persistence and cloud sync
- Feedback submission handling

### Notifications Package
- Notification scheduling and handling
- Payload processing for session IDs
- Background/foreground notification management

### App Package
- UI components and user interaction
- App lifecycle management
- Service coordination

## Testing

The implementation includes integration tests that validate:
- Immediate feedback flag handling
- Old flag cleanup (>5 minutes)
- Session feedback prioritization logic
- SharedPreferences integration

## Usage

### For Users
1. Complete a therapy session
2. Receive feedback request (immediately if app is open, or via notification)
3. Provide pain levels, satisfaction rating, and optional text feedback
4. Feedback is automatically saved locally and synced to cloud

### For Developers
The system is fully automated and requires no manual intervention. The feedback flow is triggered automatically by the session management system.

## Benefits

1. **High Feedback Collection Rate**: Immediate requests for foreground users, reliable notifications for background scenarios
2. **User-Friendly**: Only asks for feedback on the latest session, avoiding notification spam
3. **Reliable**: Handles app closure, background states, and network issues
4. **Comprehensive**: Collects structured feedback data with session context
5. **Scalable**: Integrates with existing analytics and notification infrastructure

## Future Enhancements

- Configurable notification timing
- Feedback reminder notifications for incomplete sessions
- Analytics dashboard for feedback insights
- Machine learning insights from feedback patterns
